import React, { useState, useEffect } from "react";
import {
  Input,
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  Pagination,
  Select,
  Button,
  SelectItem,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tabs,
  ModalFooter,
  Switch,
  Spinner,
  Tooltip,
  Textarea,
} from "@heroui/react";
import { useTheme } from "next-themes";
import { Key } from "react";
import { Icon } from "@iconify/react";
import { useMutation, useQuery } from "@apollo/client";

import { FilterDropdown } from "../projects/projects-table/filter-dropdown";

// Import GraphQL generated types and hooks
import {
  AllFieldsDocument,
  AllPhasesDocument,
  AllSubphasesDocument,
  ExecuteCreateFieldDocument,
  useAllFieldsQuery,
  useExecuteUpdateFieldMutation,
  useExecuteDeleteFieldMutation,
} from "@/graphql/schemas/generated";
import { useRowCountStore } from "@/store/use-row-count-store";

// Helper function to format date from ISO string to user-friendly format
const formatDate = (dateString: string): string => {
  if (!dateString) return "";

  try {
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) return "";

    // Format to local date string (DD/MM/YYYY format for Spanish locale)
    return date.toLocaleDateString("es-ES", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  } catch {
    return "";
  }
};

interface FieldProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
  canEditConfiguration: boolean;
}

interface FieldData {
  id: number;
  fieldType: string;
  name: string;
  nameEn: string; // Added English name
  description: string;
  phase: string;
  subphase: string;
  weight: number;
  milestone: boolean;
  createdAt: string;
  fieldsCount?: number;
  selectionOptions?: Array<{ text: string; countsAsCompleted: boolean }>;
  subtasks?: Array<{ title: string; description: string }>;
}

export default function Fields({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: FieldProps) {
  const { theme } = useTheme();
  const { rowCount: storeRowCount } = useRowCountStore();

  // Use the generated GraphQL query hook
  const { data: fieldsData, loading, refetch } = useAllFieldsQuery();

  const [page, setPage] = useState(1);

  // Local storage key for fields-specific rows per page
  const FIELDS_ROWS_PER_PAGE_KEY = "fields-rows-per-page";

  // Get rows per page with priority: localStorage > store > default (10)
  const getRowsPerPage = (): number => {
    if (typeof window !== "undefined") {
      const savedValue = localStorage.getItem(FIELDS_ROWS_PER_PAGE_KEY);
      if (savedValue) {
        const parsed = parseInt(savedValue, 10);
        if (!isNaN(parsed) && parsed > 0) {
          return parsed;
        }
      }
    }
    return storeRowCount || 10;
  };

  const [rowsPerPage, setRowsPerPage] = useState(getRowsPerPage());
  const [rowsPerPageInput, setRowsPerPageInput] = useState(rowsPerPage.toString());

  // Update input when store value changes and no custom value is set
  useEffect(() => {
    if (typeof window !== "undefined") {
      const hasCustomValue = localStorage.getItem(FIELDS_ROWS_PER_PAGE_KEY);
      if (!hasCustomValue) {
        const newValue = storeRowCount || 10;
        setRowsPerPage(newValue);
        setRowsPerPageInput(newValue.toString());
      }
    }
  }, [storeRowCount]);

  // Handler for rows per page change
  const handleRowsPerPageChange = (value: string) => {
    setRowsPerPageInput(value);

    // If input is empty or contains no numbers, reset to store default
    if (!value || value.trim() === "" || !/\d/.test(value)) {
      const storeDefault = storeRowCount || 10;
      setRowsPerPage(storeDefault);
      setPage(1); // Reset to first page

      // Remove from localStorage to use store default
      if (typeof window !== "undefined") {
        localStorage.removeItem(FIELDS_ROWS_PER_PAGE_KEY);
      }
      return;
    }

    const numValue = parseInt(value, 10);

    if (!isNaN(numValue) && numValue > 0) {
      setRowsPerPage(numValue);
      setPage(1); // Reset to first page

      // Save to localStorage
      if (typeof window !== "undefined") {
        localStorage.setItem(FIELDS_ROWS_PER_PAGE_KEY, numValue.toString());
      }
    }
  };

  // State for editing fields
  const [isEditing, setIsEditing] = useState(false);
  const [editingField, setEditingField] = useState<FieldData | null>(null);
  // State for viewing field details
  const [isViewingDetails, setIsViewingDetails] = useState(false);
  const [selectedField, setSelectedField] = useState<FieldData | null>(null);
  // State for deleting fields
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletingField, setDeletingField] = useState<FieldData | null>(null);

  useEffect(() => {
    refetch()
      .then(() => {
        console.log("Fields data refetched successfully");
      })
      .catch((error) => {
        console.error("Error refetching fields data:", error);
      });
  }, []);

  // Transform the GraphQL data to our FieldData format
  const fields: FieldData[] = React.useMemo(() => {
    console.log("Fields data:", fieldsData);
    if (!fieldsData?.allFields) return [];
    console.log("All fields:", fieldsData.allFields);

    return fieldsData.allFields
      .filter((field) => field !== null)
      .map((field) => {
        // Extract numeric value from weight strings like "A_1" or "A_0_5"
        let weight = 0;

        if (field.weight) {
          const weightStr = String(field.weight);
          // Extract the numeric part after the last underscore
          const match = weightStr.match(/_(\d+(?:_\d+)?)$/);

          if (match) {
            // Replace underscores with dots in the extracted number part
            const numericPart = match[1].replace("_", ".");

            weight = parseFloat(numericPart);
          } else if (!isNaN(parseFloat(weightStr))) {
            // If it's already a number or can be parsed as one
            weight = parseFloat(weightStr);
          }
        }

        // Parse selection options if available
        let selectionOptions: Array<{ text: string; countsAsCompleted: boolean }> | undefined;
        if (field.selectionOptions) {
          try {
            selectionOptions = JSON.parse(field.selectionOptions);
          } catch (error) {
            console.warn("Failed to parse selection options:", error);
            selectionOptions = undefined;
          }
        }

        // Parse subtasks if available
        let subtasks: Array<{ title: string; description: string }> | undefined;
        if (field.subtasks) {
          try {
            subtasks = JSON.parse(field.subtasks);
          } catch (error) {
            console.warn("Failed to parse subtasks:", error);
            subtasks = undefined;
          }
        }

        return {
          id: parseInt(field.id || "0"),
          fieldType: field.type || "aka", // This is missing from the query, defaulting to "Informativo"
          name: field.name || "",
          nameEn: field.nameEn || "", // This is missing from the query, using name as fallback
          description: field.description || "",
          phase: field.subphase?.phase?.name || "",
          subphase: field.subphase?.name || "",
          weight: weight,
          milestone: field.isMilestone || false,
          createdAt: formatDate(field.createdAt || ""),
          fieldsCount: field.fieldsCount || 0,
          selectionOptions,
          subtasks,
        };
      });
  }, [fieldsData]);

  // Filtering and sorting state
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  // Filter fields based on search term and active filters
  const filteredFields = fields
    .filter((field) => {
      // Apply search term filter
      if (searchTerm) {
        const searchTermLower = searchTerm.toLowerCase();

        return (
          field.name.toLowerCase().includes(searchTermLower) ||
          field.description.toLowerCase().includes(searchTermLower) ||
          field.fieldType.toLowerCase().includes(searchTermLower) ||
          field.phase.toLowerCase().includes(searchTermLower) ||
          field.subphase.toLowerCase().includes(searchTermLower) ||
          field.fieldsCount?.toString().includes(searchTermLower)
        );
      }

      return true;
    })
    .filter((field) => {
      // Apply active filters
      for (const [column, values] of Object.entries(activeFilters)) {
        if (values.length > 0) {
          const fieldValue = String(field[column as keyof FieldData]);

          if (!values.includes(fieldValue)) {
            return false;
          }
        }
      }

      return true;
    });

  // Sort fields if a sort configuration is present
  const sortedFields = [...filteredFields].sort((a, b) => {
    if (!sortConfig) return 0;

    const { column, direction } = sortConfig;

    // Handle numeric columns differently
    const numericColumns = ["id", "weight"];

    if (numericColumns.includes(column)) {
      const aValue = Number(a[column as keyof FieldData]);
      const bValue = Number(b[column as keyof FieldData]);

      if (direction === "asc") {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    } else {
      // For non-numeric columns, use string comparison
      const aValue = String(a[column as keyof FieldData]);
      const bValue = String(b[column as keyof FieldData]);

      if (direction === "asc") {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    }
  });

  const pages = Math.ceil(filteredFields.length / rowsPerPage);

  // Get current page items
  const items = sortedFields.slice(
    (page - 1) * rowsPerPage,
    page * rowsPerPage,
  );

  // Get unique values for a field to use in filters
  // This function now considers the currently filtered data to provide cascading filters
  const getUniqueValues = (column: keyof FieldData) => {
    // Use filteredFields instead of all fields to create cascading filters
    // But exclude the current column from filtering to avoid circular dependency
    const dataToUse = fields.filter((field) => {
      // Apply all active filters except for the current column
      for (const [filterColumn, values] of Object.entries(activeFilters)) {
        if (filterColumn !== column && values.length > 0) {
          const fieldValue = String(field[filterColumn as keyof FieldData]);

          if (!values.includes(fieldValue)) {
            return false;
          }
        }
      }

      // Also apply search term filter
      if (searchTerm) {
        const searchTermLower = searchTerm.toLowerCase();

        return (
          field.name.toLowerCase().includes(searchTermLower) ||
          field.description.toLowerCase().includes(searchTermLower) ||
          field.fieldType.toLowerCase().includes(searchTermLower) ||
          field.phase.toLowerCase().includes(searchTermLower) ||
          field.subphase.toLowerCase().includes(searchTermLower) ||
          field.fieldsCount?.toString().includes(searchTermLower)
        );
      }

      return true;
    });

    return Array.from(new Set(dataToUse.map((field) => String(field[column]))));
  };

  // Handle filter changes
  const handleFilterChange = (column: string, values: string[]) => {
    const newFilters = { ...activeFilters };

    if (values.length > 0) {
      newFilters[column] = values;
    } else {
      delete newFilters[column];
    }
    setActiveFilters(newFilters);
    setPage(1); // Reset to first page when filtering
  };

  // Handle sort changes
  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const handleModalConfirm = (data: AddFieldData) => {
    // Handle the data from the modal
    console.log("New field data:", data);
    // You can add logic to save the new field data here
    // setIsCreating(false);
  };

  // Handle edit field confirm
  const [updateField] = useExecuteUpdateFieldMutation();

  const handleEditConfirm = async (updatedData: {
    name: string;
    nameEn: string;
    description: string;
  }) => {
    if (!editingField) return;

    try {
      await updateField({
        variables: {
          id: editingField.id,
          input: {
            name: updatedData.name,
            nameEn: updatedData.nameEn,
            description: updatedData.description,
          },
        },
        refetchQueries: [{ query: AllFieldsDocument }],
      });

      console.log("Field updated successfully");
      setIsEditing(false);
      setEditingField(null);
    } catch (error) {
      console.error("Error updating field:", error);
      throw error; // Re-throw so the modal can handle the error
    }
  };

  // Handle showing field details
  const handleShowDetails = (field: FieldData) => {
    setSelectedField(field);
    setIsViewingDetails(true);
  };

  // Handle delete field
  const [deleteField] = useExecuteDeleteFieldMutation();

  const handleDeleteConfirm = async () => {
    if (!deletingField) return;

    try {
      await deleteField({
        variables: {
          id: deletingField.id,
        },
        refetchQueries: [{ query: AllFieldsDocument }],
      });

      console.log("Field deleted successfully");
      setIsDeleting(false);
      setDeletingField(null);
    } catch (error) {
      console.error("Error deleting field:", error);
      throw error; // Re-throw so the modal can handle the error
    }
  };

  return (
    <div className="pt-4 w-full">
      <CreateFieldModal
        isOpen={isCreating}
        onClose={() => setIsCreating(false)}
        onConfirm={handleModalConfirm}
      />

      {/* Edit Field Modal */}
      <EditFieldModal
        field={editingField}
        isOpen={isEditing}
        onClose={() => {
          setIsEditing(false);
          setEditingField(null);
        }}
        onConfirm={handleEditConfirm}
      />

      {/* Field Details Modal */}
      <FieldDetailsModal
        field={selectedField}
        isOpen={isViewingDetails}
        onClose={() => {
          setIsViewingDetails(false);
          setSelectedField(null);
        }}
      />

      {/* Delete Field Modal */}
      <DeleteFieldModal
        field={deletingField}
        isOpen={isDeleting}
        onClose={() => {
          setIsDeleting(false);
          setDeletingField(null);
        }}
        onConfirm={handleDeleteConfirm}
      />

      {/* Search input and rows per page */}
      <div className="flex w-full mb-4 gap-4">
        <Input
          isClearable
          className="flex-1"
          placeholder="Buscar campo por nombre, descripción, fase..."
          startContent={
            <Icon
              className="text-default-400"
              icon="lucide:search"
              width={18}
            />
          }
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setPage(1); // Reset to first page when searching
          }}
          onClear={() => setSearchTerm("")}
        />
        <Input
          className="w-40"
          placeholder="10"
          type="number"
          value={rowsPerPageInput}
          onChange={(e) => handleRowsPerPageChange(e.target.value)}
        />
      </div>

      <Table
        key={theme}
        removeWrapper
        aria-label="Fields table"
        bottomContent={
          <div className="flex w-full justify-center">
            <Pagination
              isCompact
              showControls
              showShadow
              color="primary"
              page={page}
              total={pages}
              onChange={(page) => setPage(page)}
            />
          </div>
        }
      >
        <TableHeader>
          <TableColumn className="w-[8%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"id"}
              items={getUniqueValues("id")}
              sortConfig={sortConfig}
              title={"#"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[15%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"fieldType"}
              displayText={{
                INFORMATIVE: "Informativo",
                SELECTION: "Selección",
                TASK_WITH_SUBTASKS: "Subtarea",
                TASK: "Tarea",
                DOCUMENT: "Documento",
              }}
              items={getUniqueValues("fieldType")}
              sortConfig={sortConfig}
              title={"Tipo de campo"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[15%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"name"}
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title={"Nombre"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[20%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"description"}
              items={getUniqueValues("description")}
              sortConfig={sortConfig}
              title={"Descripción"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[10%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"phase"}
              items={getUniqueValues("phase")}
              sortConfig={sortConfig}
              title={"Fase"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[15%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"subphase"}
              items={getUniqueValues("subphase")}
              sortConfig={sortConfig}
              title={"Subfase"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[7%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"weight"}
              items={getUniqueValues("weight")}
              sortConfig={sortConfig}
              title={"Peso"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[2%]">
            <FilterDropdown
              activeFilters={activeFilters}
              column={"milestone"}
              items={getUniqueValues("milestone")}
              sortConfig={sortConfig}
              title={"Hito"}
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="w-[2%]">Uso</TableColumn>
          <TableColumn className="w-[7%]">Última modificacion</TableColumn>
          <TableColumn className="w-[15%]">#</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent={"No se encontraron campos (╥_╥)"}
          isLoading={loading}
          items={items}
          loadingContent={<Spinner label="Cargando campos..." />}
        >
          {(item) => (
            <TableRow
              key={item.id}
              className="cursor-pointer hover:bg-default-100"
              onClick={() => handleShowDetails(item)}
            >
              <TableCell>{item.id}</TableCell>
              <TableCell>
                {item.fieldType.toLowerCase() === "informative"
                  ? "Informativo"
                  : item.fieldType.toLowerCase() === "selection"
                    ? "Selección"
                    : item.fieldType.toLowerCase() === "task_with_subtasks"
                      ? "Subtarea"
                      : item.fieldType.toLowerCase() === "task"
                        ? "Tarea"
                        : item.fieldType.toLowerCase() === "document"
                          ? "Documento"
                          : item.fieldType}
              </TableCell>
              <TableCell className="truncate max-w-0 whitespace-nowrap">
                <Tooltip
                  className="max-w-xs"
                  content={item.name}
                  placement="top"
                  showArrow={true}
                >
                  {item.name}
                </Tooltip>
              </TableCell>
              <TableCell className="truncate max-w-0 whitespace-nowrap">
                <Tooltip
                  className="max-w-xs"
                  content={item.description}
                  placement="top"
                  showArrow={true}
                >
                  {item.description}
                </Tooltip>
              </TableCell>
              <TableCell>
                {typeof item.phase === "string"
                  ? item.phase.toLowerCase() === "incubadora"
                    ? "TAKE OFF"
                    : item.phase
                  : "Sin fase"}
              </TableCell>
              <TableCell>{item.subphase}</TableCell>
              <TableCell>{item.weight}</TableCell>
              <TableCell>{item.milestone ? "Sí" : "No"}</TableCell>
              <TableCell>
                {item.fieldsCount ? item.fieldsCount : "N/A"}
              </TableCell>
              <TableCell>{item.createdAt}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    color="primary"
                    isDisabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent row click event
                      setEditingField(item);
                      setIsEditing(true);
                    }}
                  >
                    <Icon icon="lucide:edit-3" width={18} />
                  </Button>

                  <Button
                    isIconOnly
                    color="danger"
                    isDisabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent row click event
                      setDeletingField(item);
                      setIsDeleting(true);
                    }}
                  >
                    <Icon icon="lucide:trash" width={18} />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

interface AddFieldData {
  fieldType: string;
  name: string;
  nameEn: string; // Added English name
  description: string;
  phase: string;
  subphase: string;
  weight: number;
  milestone: boolean;
}

// Confirmation modal component
function ConfirmFieldModal({
  isOpen,
  onClose,
  onConfirm,
  fieldData,
  selectionOptions,
  subtaskItems,
  isLoading = false,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  fieldData: AddFieldData;
  selectionOptions: Array<{ text: string; countsAsCompleted: boolean }>;
  subtaskItems: Array<{ title: string; description: string }>;
  isLoading?: boolean;
}) {
  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <Modal isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        {() => (
          <>
            <ModalHeader>Confirmar creación de campo</ModalHeader>
            <ModalBody>
              <div className="space-y-3">
                <div>
                  <span className="font-semibold">Tipo de campo:</span>{" "}
                  <span className="break-words">{fieldData.fieldType}</span>
                </div>
                <div>
                  <span className="font-semibold">Nombre:</span>
                  <div className="mt-1 break-words whitespace-pre-wrap">
                    {fieldData.name}
                  </div>
                </div>
                <div>
                  <span className="font-semibold">Nombre en inglés:</span>
                  <div className="mt-1 break-words whitespace-pre-wrap">
                    {fieldData.nameEn}
                  </div>
                </div>
                <div>
                  <span className="font-semibold">Descripción:</span>
                  <div className="mt-1 break-words whitespace-pre-wrap">
                    {fieldData.description}
                  </div>
                </div>
                <div>
                  <span className="font-semibold">Fase:</span>{" "}
                  <span className="break-words">{fieldData.phase}</span>
                </div>
                <div>
                  <span className="font-semibold">Subfase:</span>{" "}
                  <span className="break-words">{fieldData.subphase}</span>
                </div>
                <div>
                  <span className="font-semibold">Peso:</span>{" "}
                  {fieldData.weight}
                </div>
                <div>
                  <span className="font-semibold">Hito:</span>{" "}
                  {fieldData.milestone ? "Sí" : "No"}
                </div>

                {/* Show selection options if field type is selection */}
                {fieldData.fieldType === "selection" &&
                  selectionOptions.filter((opt) => opt.text.trim() !== "")
                    .length > 0 && (
                    <div className="mt-2">
                      <div className="font-semibold">
                        Opciones de selección:
                      </div>
                      <ul className="list-disc pl-5 mt-1">
                        {selectionOptions
                          .filter((opt) => opt.text.trim() !== "")
                          .map((option, index) => (
                            <li key={index} className="flex items-center gap-2">
                              {option.text}
                              {option.countsAsCompleted && (
                                <span className="text-xs bg-success-100 text-success-700 rounded px-1 py-0.5">
                                  Computa como completado
                                </span>
                              )}
                            </li>
                          ))}
                      </ul>
                    </div>
                  )}

                {/* Show subtask items if field type is task_with_subtasks */}
                {fieldData.fieldType === "task_with_subtasks" &&
                  subtaskItems.filter((item) => item.title.trim() !== "")
                    .length > 0 && (
                    <div className="mt-2">
                      <div className="font-semibold">Subtareas:</div>
                      <div className="space-y-2 mt-1">
                        {subtaskItems
                          .filter((item) => item.title.trim() !== "")
                          .map((item, index) => (
                            <div
                              key={index}
                              className="pl-2 border-l-2 border-primary"
                            >
                              <div className="font-medium break-words whitespace-pre-wrap">
                                {item.title}
                              </div>
                              {item.description && (
                                <div className="text-sm text-gray-600 break-words whitespace-pre-wrap">
                                  {item.description}
                                </div>
                              )}
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                isDisabled={isLoading}
                variant="light"
                onPress={onClose}
              >
                Cancelar
              </Button>
              <Button
                color="primary"
                isDisabled={isLoading}
                isLoading={isLoading}
                onPress={handleConfirm}
              >
                {isLoading ? "Creando..." : "Confirmar"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

function CreateFieldModal({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: AddFieldData) => void;
}) {
  const {
    loading: loadingSubphases,
    error: errorSubphases,
    data: subphases,
  } = useQuery(AllSubphasesDocument);

  const {
    loading: loadingPhases,
    error: errorPhases,
    data: phases,
  } = useQuery(AllPhasesDocument);

  // Get existing fields data for duplicate name validation
  const { data: fieldsData } = useAllFieldsQuery();

  const [formData, setFormData] = useState<AddFieldData>({
    fieldType: "",
    name: "",
    nameEn: "",
    description: "",
    phase: "",
    subphase: "",
    weight: 0,
    milestone: false,
  });

  const [selectedFieldType, setSelectedFieldType] =
    useState<string>("informative");
  const [selectionOptions, setSelectionOptions] = useState<
    Array<{ text: string; countsAsCompleted: boolean }>
  >([{ text: "", countsAsCompleted: false }]);
  const [subtaskItems, setSubtaskItems] = useState<
    Array<{ title: string; description: string }>
  >([{ title: "", description: "" }]);
  const [formError, setFormError] = useState<string | null>(null);

  // Add state for confirmation modal
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [finalFormData, setFinalFormData] = useState<AddFieldData | null>(null);

  // Add loading state
  const [isCreating, setIsCreating] = useState(false);

  // State for duplicate name validation
  const [nameExists, setNameExists] = useState(false);

  // Check for duplicate field names
  useEffect(() => {
    if (formData.name && fieldsData?.allFields) {
      const nameExists = fieldsData.allFields.some(
        (field) =>
          field?.name?.toLowerCase() ===
          formData.name.toLowerCase().trim(),
      );

      setNameExists(nameExists);
    } else {
      setNameExists(false);
    }
  }, [formData.name, fieldsData]);

  const closeModal = () => {
    setIsCreating(false);
    resetForm();
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check if all required fields are filled
    if (
      !formData.name ||
      !formData.nameEn ||
      !formData.description ||
      !formData.phase ||
      !formData.subphase
    ) {
      setFormError("Por favor completa todos los campos requeridos");

      return;
    }

    // Check for duplicate field name
    if (nameExists) {
      setFormError("Ya existe un campo con este nombre. Por favor elige un nombre diferente.");

      return;
    }

    // For selection type, validate that we have at least one option
    if (
      selectedFieldType === "selection" &&
      selectionOptions.filter((opt) => opt.text.trim() !== "").length === 0
    ) {
      setFormError("Debes agregar al menos una opción de respuesta");

      return;
    }

    // For subtask type, validate that we have at least one item with title
    if (
      selectedFieldType === "task_with_subtasks" &&
      subtaskItems.filter((item) => item.title.trim() !== "").length === 0
    ) {
      setFormError("Debes agregar al menos un título de subtarea");

      return;
    }

    // Set the field type based on the selected tab
    const updatedFormData = {
      ...formData,
      fieldType: selectedFieldType,
    };

    // If it's a selection type, add the options to the data
    if (selectedFieldType === "selection") {
      console.log(
        "Selection options:",
        selectionOptions.filter((opt) => opt.text.trim() !== ""),
      );
    }

    // If it's a subtask type, add the items to the data
    if (selectedFieldType === "task_with_subtasks") {
      console.log(
        "Subtask items:",
        subtaskItems.filter((item) => item.title.trim() !== ""),
      );
    }

    // Store the final data and show confirmation modal
    setFinalFormData(updatedFormData);
    setShowConfirmation(true);
  };

  const [createField] = useMutation(ExecuteCreateFieldDocument);

  const handleConfirmCreate = () => {
    if (finalFormData) {
      // Create the complete data object with all form information
      const completeData = {
        ...finalFormData,
        // Include selection options if applicable
        ...(selectedFieldType === "selection" && {
          selectionOptions: selectionOptions
            .filter((opt) => opt.text.trim() !== "")
            .map((option) => ({
              text: option.text,
              countsAsCompleted: option.countsAsCompleted,
            })),
        }),
        // Include subtask items if applicable
        ...(selectedFieldType === "task_with_subtasks" && {
          subtaskItems: subtaskItems.filter((item) => item.title.trim() !== ""),
        }),
      };

      // Log the complete data
      console.log("Complete field data:", completeData);
      type MutationVariables = {
        name: string;
        nameEn: string;
        type: string;
        subphaseId: string;
        description: string;
        isMilestone: boolean;
        weight: number;
        selectionOptions: string | null;
        subtasks: string | null;
        [key: string]: any; // Add index signature
      };

      let mutationVariables: MutationVariables = {
        name: completeData.name,
        nameEn: completeData.nameEn,
        type: completeData.fieldType, // Map to lowercase string
        subphaseId: completeData.subphase,
        description: completeData.description,
        isMilestone: completeData.milestone,
        weight: completeData.weight,
        selectionOptions: completeData.selectionOptions
          ? JSON.stringify(completeData.selectionOptions)
          : null,
        subtasks: completeData.subtaskItems
          ? JSON.stringify(completeData.subtaskItems)
          : null,
      };

      for (const key in mutationVariables) {
        if (mutationVariables[key] === undefined) {
          delete mutationVariables[key];
        }
      }

      console.log("Variables being sent to mutation:", mutationVariables);

      // Set loading state to true before mutation
      setIsCreating(true);

      createField({
        variables: mutationVariables,
        refetchQueries: [
          { query: AllFieldsDocument },
          { query: AllPhasesDocument },
          { query: AllSubphasesDocument },
        ],
      })
        .then(() => {
          console.log("Field created successfully");
          setShowConfirmation(false);
          resetForm();
          onClose();
          // No need to manually call refetch since we included AllFieldsDocument in refetchQueries
        })
        .catch((error) => {
          console.error("Error creating field:", error);
          setFormError("Error al crear el campo. Inténtalo de nuevo.");
        })
        .finally(() => {
          setIsCreating(false);
        });
    }
  };

  const resetForm = () => {
    setFormData({
      fieldType: "",
      name: "",
      nameEn: "",
      description: "",
      phase: "",
      subphase: "",
      weight: 0,
      milestone: false,
    });
    setSelectionOptions([{ text: "", countsAsCompleted: false }]);
    setSubtaskItems([{ title: "", description: "" }]);
    setFormError(null);
  };

  const handleFieldTypeChange = (key: Key) => {
    setSelectedFieldType(key.toString());
    // Reset the form error when changing field type
    setFormError(null);
  };

  const addSelectionOption = () => {
    // Simply add a new empty option at the end
    setSelectionOptions([
      ...selectionOptions,
      { text: "", countsAsCompleted: false },
    ]);
  };

  const updateSelectionOption = (index: number, value: string) => {
    const newOptions = [...selectionOptions];

    newOptions[index].text = value;
    setSelectionOptions(newOptions);
  };

  const toggleCountsAsCompleted = (index: number, value: boolean) => {
    const newOptions = [...selectionOptions];

    newOptions[index].countsAsCompleted = value;
    setSelectionOptions(newOptions);
  };

  const removeSelectionOption = (index: number) => {
    if (selectionOptions.length > 1) {
      const newOptions = selectionOptions.filter((_, i) => i !== index);

      setSelectionOptions(newOptions);
    }
  };

  // Subtask item handlers
  const addSubtaskItem = () => {
    setSubtaskItems([...subtaskItems, { title: "", description: "" }]);
  };

  const updateSubtaskItem = (
    index: number,
    field: "title" | "description",
    value: string,
  ) => {
    const newItems = [...subtaskItems];

    newItems[index][field] = value;
    setSubtaskItems(newItems);
  };

  const removeSubtaskItem = (index: number) => {
    if (subtaskItems.length > 1) {
      const newItems = subtaskItems.filter((_, i) => i !== index);

      setSubtaskItems(newItems);
    }
  };

  const renderPhasesSelect = () => {
    return (
      <Select
        isRequired
        className="w-full mt-4"
        isDisabled={loadingPhases}
        isLoading={loadingPhases}
        label="Fase"
        labelPlacement="outside"
        placeholder="Seleccionar fase"
        value={formData.phase}
        onChange={(e) => {
          const phaseId = e.target.value;

          setFormData((prev) => {
            // If phase changes, reset subphase
            return {
              ...prev,
              phase: phaseId,
              subphase: "",
            };
          });
        }}
      >
        {phases?.allPhases?.map(
          (phase: {
            id: React.Key | null | undefined;
            name:
              | string
              | number
              | bigint
              | boolean
              | React.ReactElement<
                  any,
                  string | React.JSXElementConstructor<any>
                >
              | Iterable<React.ReactNode>
              | React.ReactPortal
              | Promise<React.AwaitedReactNode>
              | null
              | undefined;
          }) => (
            <SelectItem key={phase?.id}>
              {typeof phase?.name === "string"
                ? phase.name.toLowerCase() === "incubadora"
                  ? "TAKE OFF"
                  : phase.name
                : "Sin nombre"}
            </SelectItem>
          ),
        )}
      </Select>
    );
  };

  const renderSubphasesSelect = () => {
    return (
      <Select
        isRequired
        className="w-full mt-4"
        isDisabled={loadingSubphases || !formData.phase}
        isLoading={loadingSubphases}
        label="Subfase"
        labelPlacement="outside"
        placeholder="Seleccionar subfase"
        value={formData.subphase}
        onChange={(e) => {
          const subphaseId = e.target.value;

          setFormData((prev) => ({
            ...prev,
            subphase: subphaseId,
          }));

          // If phase is not selected, auto-select the phase based on the subphase
          if (!formData.phase && subphaseId) {
            const selectedSubphase = subphases?.allSubphases?.find(
              (subphase: { id: string }) => subphase?.id === subphaseId,
            );

            if (selectedSubphase?.phase?.id) {
              setFormData((prev) => ({
                ...prev,
                phase: selectedSubphase.phase.id,
              }));
            }
          }
        }}
      >
        {subphases?.allSubphases
          ?.filter(
            (subphase: { phase: { id: string } }) =>
              !formData.phase || // Show all if no phase is selected
              subphase?.phase?.id === formData.phase,
          )
          .map(
            (subphase: {
              id: React.Key | null | undefined;
              name:
                | string
                | number
                | bigint
                | boolean
                | React.ReactElement<
                    any,
                    string | React.JSXElementConstructor<any>
                  >
                | Iterable<React.ReactNode>
                | React.ReactPortal
                | Promise<React.AwaitedReactNode>
                | null
                | undefined;
            }) => <SelectItem key={subphase?.id}>{subphase?.name}</SelectItem>,
          )}
      </Select>
    );
  };

  return (
    <>
      <Modal
        backdrop="opaque"
        classNames={{
          body: "overflow-y-auto max-h-[calc(80vh-8rem)]", // Add max height constraint
          base: "max-h-[80vh]", // Limit overall modal height
          wrapper: "overflow-hidden",
        }}
        isOpen={isOpen}
        scrollBehavior="inside"
        size="xl"
        onClose={closeModal}
      >
        <ModalContent>
          {(closeModal) => (
            <form
              className="overflow-hidden flex flex-col h-full"
              onSubmit={handleSubmit}
            >
              <ModalHeader className="flex-shrink-0">
                Crear nuevo campo
              </ModalHeader>
              <ModalBody className="flex-grow overflow-y-auto">
                <div className="w-full">
                  <Tabs
                    key="solid"
                    aria-label="Opciones de configuración"
                    className="w-full"
                    selectedKey={selectedFieldType}
                    variant="solid"
                    onSelectionChange={handleFieldTypeChange}
                  >
                    <Tab key="informative" title="Informativo" />
                    <Tab key="selection" title="Selección" />
                    <Tab key="task" title="Tarea" />
                    <Tab key="document" title="Documento" />
                    <Tab key="task_with_subtasks" title="Subtarea" />
                  </Tabs>

                  {formError && (
                    <div className="text-danger mt-2 text-sm">{formError}</div>
                  )}

                  <Input
                    isRequired
                    className="w-full mt-4 pt-2"
                    color={nameExists ? "danger" : "default"}
                    description={
                      nameExists
                        ? "Este nombre ya existe"
                        : formData.name.length > 30
                          ? `${formData.name.length}/40 caracteres`
                          : ""
                    }
                    errorMessage={nameExists ? "Ya existe un campo con este nombre" : ""}
                    isInvalid={nameExists}
                    label="Nombre del campo"
                    labelPlacement="outside"
                    maxLength={40}
                    placeholder="Insertar nombre del campo"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                  />
                  <Input
                    isRequired
                    className="w-full mt-4 pt-2"
                    description={
                      formData.name.length > 30
                        ? `${formData.name.length}/40 caracteres`
                        : ""
                    }
                    label="Nombre en inglés"
                    labelPlacement="outside"
                    maxLength={40}
                    placeholder="Insertar nombre en inglés"
                    value={formData.nameEn}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        nameEn: e.target.value,
                      }))
                    }
                  />
                  <Textarea
                    isRequired
                    className="w-full mt-4 pt-2"
                    description={`${formData.description.length}/140 caracteres`}
                    label="Descripción del campo"
                    labelPlacement="outside"
                    maxLength={140}
                    placeholder="Insertar descripción del campo"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />

                  <div className="w-full flex gap-2 items-end pt-2">
                    {renderPhasesSelect()}
                    {renderSubphasesSelect()}
                  </div>

                  <div className="w-full flex gap-2 items-end pt-2">
                    <Select
                      isRequired
                      className="w-full mt-4"
                      label="Peso"
                      labelPlacement="outside"
                      placeholder="Seleccionar peso"
                      value={formData.weight.toString()}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          weight: parseFloat(e.target.value),
                        }))
                      }
                    >
                      <SelectItem key="0.25">0.25</SelectItem>
                      <SelectItem key="0.5">0.5</SelectItem>
                      <SelectItem key="0.75">0.75</SelectItem>
                      <SelectItem key="1">1</SelectItem>
                    </Select>
                    <Select
                      isRequired
                      className="w-full mt-4"
                      label="Hito"
                      labelPlacement="outside"
                      placeholder="Seleccionar hito"
                      value={formData.milestone ? "Sí" : "No"}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          milestone: e.target.value === "Sí",
                        }))
                      }
                    >
                      <SelectItem key="Sí">Sí</SelectItem>
                      <SelectItem key="No">No</SelectItem>
                    </Select>
                  </div>

                  {selectedFieldType === "selection" && (
                    <div className="w-full mt-4">
                      <div className="text-sm mb-2 font-medium">
                        Posibles respuestas
                      </div>
                      {selectionOptions.map((option, index) => (
                        <div
                          key={index}
                          className="w-full flex gap-2 items-end mt-2"
                        >
                          <Input
                            className="w-full"
                            placeholder={`Opción ${index + 1}`}
                            value={option.text}
                            onChange={(e) =>
                              updateSelectionOption(index, e.target.value)
                            }
                          />
                          {index === selectionOptions.length - 1 ? (
                            <Button
                              isIconOnly
                              aria-label="Add option"
                              color="primary"
                              type="button"
                              onPress={addSelectionOption}
                            >
                              +
                            </Button>
                          ) : (
                            <div className="flex items-center gap-2">
                              <Switch
                                aria-label="Computa como completado"
                                color="success"
                                endContent={<Icon icon="lucide:x" />}
                                isSelected={option.countsAsCompleted}
                                startContent={<Icon icon="lucide:check" />}
                                onChange={(e) =>
                                  toggleCountsAsCompleted(
                                    index,
                                    e.target.checked,
                                  )
                                }
                              />
                              <Button
                                isIconOnly
                                aria-label="Remove option"
                                color="danger"
                                type="button"
                                onPress={() => removeSelectionOption(index)}
                              >
                                -
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                      <div className="text-xs text-gray-500 mt-2">
                        Usa el interruptor para indicar qué opciones computan
                        como completadas
                      </div>
                    </div>
                  )}

                  {selectedFieldType === "task_with_subtasks" && (
                    <div className="w-full mt-4">
                      <div className="text-sm mb-2 font-medium">Subtareas</div>
                      {subtaskItems.map((item, index) => (
                        <div
                          key={index}
                          className="w-full flex flex-col gap-2 mt-3 p-3 border border-default-200 rounded-md"
                        >
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">
                              Subtarea {index + 1}
                            </span>
                            {index > 0 && (
                              <Button
                                isIconOnly
                                aria-label="Remove subtask"
                                color="danger"
                                size="sm"
                                type="button"
                                onPress={() => removeSubtaskItem(index)}
                              >
                                -
                              </Button>
                            )}
                          </div>
                          <Input
                            className="w-full mt-1"
                            label="Título"
                            placeholder="Título de la subtarea"
                            value={item.title}
                            onChange={(e) =>
                              updateSubtaskItem(index, "title", e.target.value)
                            }
                          />
                          <Input
                            className="w-full mt-1"
                            label="Descripción"
                            placeholder="Descripción de la subtarea"
                            value={item.description}
                            onChange={(e) =>
                              updateSubtaskItem(
                                index,
                                "description",
                                e.target.value,
                              )
                            }
                          />
                          {index === subtaskItems.length - 1 && (
                            <Button
                              className="mt-2 w-full"
                              color="primary"
                              type="button"
                              variant="flat"
                              onPress={addSubtaskItem}
                            >
                              + Agregar otra subtarea
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </ModalBody>
              <ModalFooter className="flex-shrink-0">
                <Button
                  className="mr-2"
                  color="danger"
                  type="button"
                  variant="light"
                  onPress={closeModal}
                >
                  Cancelar
                </Button>
                <Button
                  color="primary"
                  isDisabled={nameExists}
                  type="submit"
                >
                  Crear campo
                </Button>
              </ModalFooter>
            </form>
          )}
        </ModalContent>
      </Modal>

      {/* Confirmation modal */}
      {finalFormData && (
        <ConfirmFieldModal
          fieldData={finalFormData}
          isLoading={isCreating}
          isOpen={showConfirmation}
          selectionOptions={selectionOptions}
          subtaskItems={subtaskItems}
          onClose={() => setShowConfirmation(false)}
          onConfirm={handleConfirmCreate}
        />
      )}
    </>
  );
}

function EditFieldModal({
  isOpen,
  onClose,
  onConfirm,
  field,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (updatedData: {
    name: string;
    nameEn: string;
    description: string;
  }) => void;
  field: FieldData | null;
}) {
  // Get existing fields data for duplicate name validation
  const { data: fieldsData } = useAllFieldsQuery();

  const [formData, setFormData] = useState<{
    name: string;
    nameEn: string;
    description: string;
  }>({ name: "", nameEn: "", description: "" });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for duplicate name validation
  const [nameExists, setNameExists] = useState(false);

  // Check for duplicate field names (excluding the current field being edited)
  useEffect(() => {
    if (formData.name && fieldsData?.allFields && field) {
      const nameExists = fieldsData.allFields.some(
        (existingField) =>
          existingField?.name?.toLowerCase() ===
          formData.name.toLowerCase().trim() &&
          existingField?.id !== field.id.toString() // Exclude current field
      );

      setNameExists(nameExists);
    } else {
      setNameExists(false);
    }
  }, [formData.name, fieldsData, field]);

  // Update form data when the field changes or modal opens
  useEffect(() => {
    if (field && isOpen) {
      setFormData({
        name: field.name,
        nameEn: field.nameEn,
        description: field.description,
      });
      setError(null); // Clear any previous errors
      setNameExists(false); // Reset name validation
    }
  }, [field, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that fields are not empty
    if (
      !formData.name.trim() ||
      !formData.nameEn.trim() ||
      !formData.description.trim()
    ) {
      setError("Todos los campos son requeridos");

      return;
    }

    // Check for duplicate field name
    if (nameExists) {
      setError("Ya existe un campo con este nombre. Por favor elige un nombre diferente.");

      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await onConfirm(formData);
    } catch (err) {
      setError("Error al actualizar el campo. Inténtalo de nuevo.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        {() => (
          <form onSubmit={handleSubmit}>
            <ModalHeader>Editar campo</ModalHeader>
            <ModalBody>
              {error && (
                <div className="text-danger text-sm mb-4 p-2 bg-danger-50 rounded">
                  {error}
                </div>
              )}
              <Input
                isRequired
                className="w-full mt-4"
                color={nameExists ? "danger" : "default"}
                errorMessage={nameExists ? "Ya existe un campo con este nombre" : ""}
                isDisabled={isLoading}
                isInvalid={nameExists}
                label="Nombre del campo"
                labelPlacement="outside"
                placeholder="Insertar nombre del campo"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
              />
              <Input
                isRequired
                className="w-full mt-4"
                isDisabled={isLoading}
                label="Nombre en inglés"
                labelPlacement="outside"
                placeholder="Insertar nombre en inglés"
                value={formData.nameEn}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, nameEn: e.target.value }))
                }
              />
              <Textarea
                isRequired
                className="w-full mt-4"
                isDisabled={isLoading}
                label="Descripción del campo"
                labelPlacement="outside"
                placeholder="Insertar descripción del campo"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
              />
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                isDisabled={isLoading}
                variant="light"
                onPress={onClose}
              >
                Cancelar
              </Button>
              <Button
                color="primary"
                isDisabled={isLoading || nameExists}
                isLoading={isLoading}
                type="submit"
              >
                {isLoading ? "Guardando..." : "Guardar cambios"}
              </Button>
            </ModalFooter>
          </form>
        )}
      </ModalContent>
    </Modal>
  );
}

function FieldDetailsModal({
  isOpen,
  onClose,
  field,
}: {
  isOpen: boolean;
  onClose: () => void;
  field: FieldData | null;
}) {
  return (
    <Modal
      classNames={{
        body: "py-6",
      }}
      isOpen={isOpen}
      size="md"
      onClose={onClose}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1 pb-2 border-b">
              <h3 className="text-xl font-medium">Detalles del campo</h3>
              {field && (
                <p className="text-small text-default-500">
                  ID: {field.id} · Tipo: {field.fieldType}
                </p>
              )}
            </ModalHeader>
            <ModalBody>
              {field ? (
                <div className="space-y-5">
                  <div className="space-y-3 pb-4 border-b">
                    <h4 className="text-medium font-medium text-default-700">
                      Información básica
                    </h4>
                    <div className="grid grid-cols-1 gap-3">
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Nombre
                        </p>
                        <p className="text-medium">{field.name}</p>
                      </div>
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Nombre en inglés
                        </p>
                        <p className="text-medium">{field.nameEn}</p>
                      </div>
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Descripción
                        </p>
                        <p className="text-medium">{field.description}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 pb-4 border-b">
                    <h4 className="text-medium font-medium text-default-700">
                      Categorización
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Fase
                        </p>
                        {/*  */}
                        <p className="text-medium">
                          {typeof field.phase === "string"
                            ? field.phase.toLowerCase()
                            : field.phase || "-"}
                        </p>
                      </div>
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Subfase
                        </p>
                        <p className="text-medium">{field.subphase || "-"}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3 pb-4 border-b">
                    <h4 className="text-medium font-medium text-default-700">
                      Propiedades
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Peso
                        </p>
                        <p className="text-medium">{field.weight}</p>
                      </div>
                      <div>
                        <p className="text-small font-medium text-default-500">
                          Hito
                        </p>
                        <div
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-sm ${
                            field.milestone
                              ? "bg-success-100 text-success-700"
                              : "bg-default-100 text-default-700"
                          }`}
                        >
                          {field.milestone ? (
                            <>
                              <Icon icon="lucide:check-circle" width={16} />
                              <span>Sí</span>
                            </>
                          ) : (
                            <>
                              <Icon icon="lucide:circle" width={16} />
                              <span>No</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Show selection options if field type is selection */}
                  {field.fieldType?.toLowerCase() === "selection" && field.selectionOptions && field.selectionOptions.length > 0 && (
                    <div className="space-y-3 pb-4 border-b">
                      <h4 className="text-medium font-medium text-default-700">
                        Opciones de selección
                      </h4>
                      <div className="space-y-2">
                        {field.selectionOptions.map((option, index) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-default-50 rounded-md">
                            <span className="flex-1">{option.text}</span>
                            {option.countsAsCompleted && (
                              <span className="text-xs bg-success-100 text-success-700 rounded px-2 py-1">
                                Computa como completado
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Show subtasks if field type is task_with_subtasks */}
                  {field.fieldType?.toLowerCase() === "task_with_subtasks" && field.subtasks && field.subtasks.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-medium font-medium text-default-700">
                        Subtareas
                      </h4>
                      <div className="space-y-3">
                        {field.subtasks.map((subtask, index) => (
                          <div key={index} className="p-3 bg-default-50 rounded-md border-l-4 border-primary">
                            <div className="font-medium text-default-700 mb-1">
                              {subtask.title}
                            </div>
                            {subtask.description && (
                              <div className="text-sm text-default-500">
                                {subtask.description}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="py-8 text-center text-default-400">
                  <Icon
                    className="mx-auto mb-2"
                    icon="lucide:file-question"
                    width={24}
                  />
                  <p>No hay detalles disponibles</p>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button color="primary" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

function DeleteFieldModal({
  isOpen,
  onClose,
  onConfirm,
  field,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  field: FieldData | null;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = async () => {
    if (!field) return;

    setIsLoading(true);
    setError(null);

    try {
      await onConfirm();
    } catch (err) {
      setError("Error al eliminar el campo. Inténtalo de nuevo.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        {() => (
          <>
            <ModalHeader>Eliminar campo</ModalHeader>
            <ModalBody>
              {error && (
                <div className="text-danger text-sm mb-4 p-2 bg-danger-50 rounded">
                  {error}
                </div>
              )}
              {field && (
                <div className="space-y-3">
                  <p>¿Estás seguro de que deseas eliminar este campo?</p>
                  <div className="bg-default-100 p-3 rounded">
                    <div>
                      <span className="font-semibold">Nombre:</span>{" "}
                      {field.name}
                    </div>
                    <div>
                      <span className="font-semibold">Descripción:</span>{" "}
                      {field.description}
                    </div>
                    <div>
                      <span className="font-semibold">Tipo:</span>{" "}
                      {field.fieldType}
                    </div>
                  </div>
                  <p className="text-danger text-sm">
                    Esta acción no se puede deshacer.
                  </p>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button
                color="default"
                isDisabled={isLoading}
                variant="light"
                onPress={onClose}
              >
                Cancelar
              </Button>
              <Button
                color="danger"
                isDisabled={isLoading}
                isLoading={isLoading}
                onPress={handleConfirm}
              >
                {isLoading ? "Eliminando..." : "Eliminar"}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
